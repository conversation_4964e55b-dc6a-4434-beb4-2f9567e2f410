<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>独立左右列布局测试 - OTA订单处理系统</title>
    <link rel="stylesheet" href="style.css">
    <style>
        /* 测试页面专用样式 */
        .test-container {
            padding: var(--spacing-4);
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .test-section {
            background: var(--bg-tertiary);
            border-radius: var(--radius-lg);
            box-shadow: var(--neu-shadow-outset);
            padding: var(--spacing-4);
            margin-bottom: var(--spacing-4);
        }
        
        .test-title {
            color: var(--text-primary);
            font-size: var(--font-size-lg);
            font-weight: 600;
            margin-bottom: var(--spacing-3);
            border-bottom: 2px solid var(--color-primary);
            padding-bottom: var(--spacing-2);
        }
        
        .test-controls {
            display: flex;
            gap: var(--spacing-2);
            margin-bottom: var(--spacing-3);
            justify-content: center;
        }
        
        .test-info {
            background: var(--bg-primary);
            border-radius: var(--radius-md);
            padding: var(--spacing-2);
            margin-top: var(--spacing-2);
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🏗️ 独立左右列布局系统测试</h1>
        
        <!-- 布局说明 -->
        <div class="test-section">
            <h2 class="test-title">布局结构说明</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-4); margin-bottom: var(--spacing-3);">
                <div style="background: var(--bg-primary); padding: var(--spacing-3); border-radius: var(--radius-md);">
                    <h3 style="color: var(--color-primary); margin-bottom: var(--spacing-2);">左列（3个板块）</h3>
                    <ul style="margin: 0; padding-left: var(--spacing-4); color: var(--text-secondary);">
                        <li>📝 订单输入</li>
                        <li>🚗 行程信息</li>
                        <li>🔧 特殊需求</li>
                    </ul>
                </div>
                <div style="background: var(--bg-primary); padding: var(--spacing-3); border-radius: var(--radius-md);">
                    <h3 style="color: var(--color-primary); margin-bottom: var(--spacing-2);">右列（4个板块）</h3>
                    <ul style="margin: 0; padding-left: var(--spacing-4); color: var(--text-secondary);">
                        <li>📋 基本信息</li>
                        <li>👤 客户信息</li>
                        <li>⚙️ 服务配置</li>
                        <li>📝 额外要求</li>
                    </ul>
                </div>
            </div>
            <p class="test-info">
                每列内的板块可以独立调整高度，拖拽板块底部边缘即可调整。左右两列完全独立，互不影响。
            </p>
        </div>
        
        <!-- 实际布局测试 -->
        <div class="test-section">
            <h2 class="test-title">实际布局测试</h2>
            
            <div class="test-controls">
                <button type="button" id="resetLayoutBtn" class="btn btn-outline btn-sm">重置布局</button>
                <button type="button" id="showLayoutInfoBtn" class="btn btn-outline btn-sm">显示布局信息</button>
                <button type="button" id="themeToggleBtn" class="btn btn-outline btn-sm">切换主题</button>
            </div>
            
            <form class="grid-container-new" style="height: 600px;">
                <!-- 左列 -->
                <div class="column-left">
                    <!-- 订单输入板块 -->
                    <section class="panel" data-panel="order-input">
                        <div class="resize-handle resize-handle-bottom"></div>
                        
                        <div class="section-header">
                            <h3>📝 订单输入</h3>
                        </div>
                        <div class="panel-content">
                            <div class="form-group">
                                <label>订单描述</label>
                                <div class="textarea-container">
                                    <textarea rows="3" placeholder="在这里输入订单描述..."></textarea>
                                    <button type="button" class="textarea-upload-button" title="上传图片">
                                        <span class="upload-icon">📁</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- 行程信息板块 -->
                    <section class="panel" data-panel="trip-info">
                        <div class="resize-handle resize-handle-bottom"></div>
                        
                        <div class="section-header">
                            <h3>🚗 行程信息</h3>
                        </div>
                        <div class="panel-content">
                            <div class="form-group">
                                <label>上车地点</label>
                                <input type="text" placeholder="上车地点">
                            </div>
                            <div class="form-group">
                                <label>目的地</label>
                                <input type="text" placeholder="目的地">
                            </div>
                            <div class="form-group">
                                <label>接送日期</label>
                                <input type="date">
                            </div>
                            <div class="form-group">
                                <label>接送时间</label>
                                <input type="time">
                            </div>
                        </div>
                    </section>

                    <!-- 特殊需求板块 -->
                    <section class="panel" data-panel="special-requirements">
                        <div class="section-header">
                            <h3>🔧 特殊需求</h3>
                        </div>
                        <div class="panel-content">
                            <div class="form-group">
                                <label>特殊要求选项</label>
                                <div class="checkbox-group-vertical">
                                    <label class="checkbox-label">
                                        <input type="checkbox">
                                        <span>儿童座椅</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox">
                                        <span>导游服务</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox">
                                        <span>迎接服务</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>

                <!-- 右列 -->
                <div class="column-right">
                    <!-- 基本信息板块 -->
                    <section class="panel" data-panel="basic-info">
                        <div class="resize-handle resize-handle-bottom"></div>
                        
                        <div class="section-header">
                            <h3>📋 基本信息</h3>
                        </div>
                        <div class="panel-content">
                            <div class="form-group">
                                <label>服务类型</label>
                                <select>
                                    <option>请选择服务类型</option>
                                    <option>接机服务</option>
                                    <option>送机服务</option>
                                    <option>包车服务</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>OTA参考号</label>
                                <input type="text" placeholder="OTA平台订单号">
                            </div>
                            <div class="form-group">
                                <label>OTA渠道</label>
                                <select>
                                    <option>请选择OTA渠道</option>
                                    <option>Booking.com</option>
                                    <option>Agoda</option>
                                    <option>Expedia</option>
                                </select>
                            </div>
                        </div>
                    </section>

                    <!-- 客户信息板块 -->
                    <section class="panel" data-panel="customer-info">
                        <div class="resize-handle resize-handle-bottom"></div>
                        
                        <div class="section-header">
                            <h3>👤 客户信息</h3>
                        </div>
                        <div class="panel-content">
                            <div class="form-group">
                                <label>客户姓名</label>
                                <input type="text" placeholder="客户姓名">
                            </div>
                            <div class="form-group">
                                <label>联系电话</label>
                                <input type="tel" placeholder="联系电话">
                            </div>
                            <div class="form-group">
                                <label>客户邮箱</label>
                                <input type="email" placeholder="客户邮箱">
                            </div>
                            <div class="form-group">
                                <label>航班信息</label>
                                <input type="text" placeholder="航班号/航班信息">
                            </div>
                        </div>
                    </section>

                    <!-- 服务配置板块 -->
                    <section class="panel" data-panel="service-config">
                        <div class="resize-handle resize-handle-bottom"></div>
                        
                        <div class="section-header">
                            <h3>⚙️ 服务配置</h3>
                        </div>
                        <div class="panel-content">
                            <div class="form-group">
                                <label>乘客人数</label>
                                <input type="number" placeholder="乘客人数" min="1" max="20">
                            </div>
                            <div class="form-group">
                                <label>行李件数</label>
                                <input type="number" placeholder="行李件数" min="0" max="50">
                            </div>
                            <div class="form-group">
                                <label>价格</label>
                                <div class="price-input-group">
                                    <input type="number" placeholder="价格" step="0.01" min="0">
                                    <select>
                                        <option value="MYR">MYR</option>
                                        <option value="USD">USD</option>
                                        <option value="CNY">CNY</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- 额外要求板块 -->
                    <section class="panel" data-panel="extra-requirements">
                        <div class="section-header">
                            <h3>📝 额外要求</h3>
                        </div>
                        <div class="panel-content">
                            <div class="form-group">
                                <label>额外要求</label>
                                <textarea rows="4" placeholder="其他特殊要求或备注"></textarea>
                            </div>
                        </div>
                    </section>
                </div>
            </form>
            
            <p class="test-info">
                拖拽板块底部边缘来调整高度。每列内的板块会自动贴合，无间隙。左右两列完全独立调整。
            </p>
        </div>
        
        <!-- 操作按钮区域测试 -->
        <div class="test-section">
            <h2 class="test-title">操作按钮区域</h2>
            <div class="action-section">
                <div class="form-actions">
                    <button type="button" class="btn btn-outline">
                        <span>⚠️ 数据异常提示</span>
                    </button>
                    <button type="button" class="btn btn-primary">
                        <span>✅ 创建订单</span>
                    </button>
                </div>
            </div>
            <p class="test-info">
                操作按钮区域位于所有板块下方，横跨整个宽度。
            </p>
        </div>
    </div>
    
    <!-- 加载必要的脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/grid-resizer.js"></script>
    
    <script>
        // 初始化测试页面
        let testGridResizer = null;
        
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化网格调整器
            if (window.OTA && window.OTA.GridResizer) {
                testGridResizer = new window.OTA.GridResizer();
                console.log('测试页面网格调整器已初始化');
            }
            
            // 重置布局按钮
            const resetBtn = document.getElementById('resetLayoutBtn');
            if (resetBtn) {
                resetBtn.addEventListener('click', function() {
                    if (testGridResizer) {
                        testGridResizer.resetGrid();
                        alert('布局已重置为默认尺寸');
                    }
                });
            }
            
            // 显示布局信息按钮
            const infoBtn = document.getElementById('showLayoutInfoBtn');
            if (infoBtn) {
                infoBtn.addEventListener('click', function() {
                    if (testGridResizer) {
                        const sizes = testGridResizer.getCurrentColumnSizes();
                        let info = '当前布局信息：\n\n';
                        if (sizes.left) {
                            info += '左列板块比例：\n';
                            sizes.left.forEach((size, index) => {
                                const panels = ['订单输入', '行程信息', '特殊需求'];
                                info += `  ${panels[index]}: ${size.toFixed(2)}fr\n`;
                            });
                        }
                        if (sizes.right) {
                            info += '\n右列板块比例：\n';
                            sizes.right.forEach((size, index) => {
                                const panels = ['基本信息', '客户信息', '服务配置', '额外要求'];
                                info += `  ${panels[index]}: ${size.toFixed(2)}fr\n`;
                            });
                        }
                        alert(info);
                    }
                });
            }
            
            // 主题切换按钮
            const themeBtn = document.getElementById('themeToggleBtn');
            if (themeBtn) {
                themeBtn.addEventListener('click', function() {
                    const currentTheme = document.documentElement.getAttribute('data-theme');
                    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                    document.documentElement.setAttribute('data-theme', newTheme);
                    this.textContent = newTheme === 'dark' ? '☀️ 亮色主题' : '🌙 暗色主题';
                });
            }
        });
    </script>
</body>
</html>
