/**
 * 国际化管理器
 * 负责多语言支持和文本翻译
 * <AUTHOR>
 * @version 1.0.0
 */

// 获取依赖模块（延迟获取以确保加载顺序）
function getLogger() {
    return window.OTA && window.OTA.logger || window.logger;
}

class I18nManager {
    constructor() {
        this.currentLanguage = 'zh';
        this.storageKey = 'ota_language_preference';
        this.translations = {};
        this.init();
    }

    /**
     * 初始化国际化管理器
     */
    init() {
        // 加载语言资源
        this.loadTranslations();
        
        // 从localStorage加载用户语言偏好
        const savedLanguage = localStorage.getItem(this.storageKey);
        if (savedLanguage && this.translations[savedLanguage]) {
            this.currentLanguage = savedLanguage;
        }
        
        const logger = getLogger();
        if (logger) {
            logger.log(`国际化管理器已初始化，当前语言: ${this.currentLanguage}`, 'info');
        }
    }

    /**
     * 加载翻译资源
     */
    loadTranslations() {
        this.translations = {
            zh: {
                // 通用
                'common.loading': '加载中...',
                'common.success': '成功',
                'common.error': '错误',
                'common.warning': '警告',
                'common.info': '信息',
                'common.confirm': '确认',
                'common.cancel': '取消',
                'common.close': '关闭',
                'common.save': '保存',
                'common.delete': '删除',
                'common.edit': '编辑',
                'common.search': '搜索',
                'common.reset': '重置',
                'common.export': '导出',
                'common.clear': '清空',
                'common.copy': '复制',
                'common.view': '查看',
                
                // 头部导航
                'header.title': 'OTA订单处理系统',
                'header.defaultEmail': '默认邮箱:',
                'header.defaultEmailPlaceholder': '设置默认客户邮箱',
                'header.defaultEmailTooltip': '设置默认客户邮箱，当AI解析无法获取邮箱时自动使用',
                'header.historyOrders': '历史订单',
                'header.logout': '退出登录',
                'header.toggleTheme': '切换主题',
                'header.language': '选择语言',
                'header.languageZh': '中文',
                'header.languageEn': 'English',
                
                // 登录界面
                'login.title': '用户登录',
                'login.email': '邮箱地址',
                'login.password': '密码',
                'login.rememberMe': '记住登录状态',
                'login.loginButton': '登录',
                'login.clearSaved': '清除保存的账号',
                'login.emailPlaceholder': '请输入邮箱地址',
                'login.passwordPlaceholder': '请输入密码',
                
                // 智能输入区
                'input.title': '🤖 智能订单解析',
                'input.placeholder': '请输入订单描述文本，系统将自动解析订单信息...',
                'input.clearButton': '清空',
                'input.sampleButton': '示例数据',
                'input.parseButton': '手动解析',
                'input.geminiStatus': '请输入订单描述',
                
                // 订单预览
                'preview.title': '📋 订单预览与编辑',
                'preview.validate': '验证数据',
                'preview.reset': '重置',
                'preview.close': '关闭',
                
                // 表单字段
                'form.basicInfo': '基本信息',
                'form.subCategory': '子分类',
                'form.subCategoryPlaceholder': '请选择子分类',
                'form.otaReference': 'OTA参考号',
                'form.otaReferencePlaceholder': 'GMH-',
                'form.otaChannel': 'OTA渠道',
                'form.otaChannelPlaceholder': '请选择OTA渠道',
                'form.otaChannelCustom': '自定义OTA',
                'form.carType': '车型',
                'form.carTypePlaceholder': '请选择车型',
                'form.incharge': '负责人',
                'form.inchargePlaceholder': '请选择负责人',
                
                'form.customerInfo': '客户信息',
                'form.customerName': '客户姓名',
                'form.customerNamePlaceholder': '客户姓名',
                'form.customerContact': '联系电话',
                'form.customerContactPlaceholder': '+60123456789',
                'form.customerEmail': '客户邮箱',
                'form.customerEmailPlaceholder': '<EMAIL>',
                'form.flightInfo': '航班信息',
                'form.flightInfoPlaceholder': 'MH123',
                
                'form.tripInfo': '行程信息',
                'form.pickup': '上车地点',
                'form.pickupPlaceholder': '上车地点',
                'form.destination': '目的地',
                'form.destinationPlaceholder': '目的地',
                'form.date': '日期',
                'form.time': '时间',
                'form.passengerNumber': '乘客人数',
                'form.luggageNumber': '行李数量',
                'form.specialRequests': '特殊要求',
                'form.specialRequestsPlaceholder': '特殊要求或备注',
                
                'form.additionalInfo': '附加信息',
                'form.otaPrice': 'OTA价格',
                'form.otaPricePlaceholder': '0.00',
                'form.drivingRegion': '行驶区域',
                'form.drivingRegionPlaceholder': '请选择行驶区域',
                'form.languages': '语言',
                'form.languagesPlaceholder': '请选择语言',
                
                // 操作按钮
                'actions.previewOrder': '预览订单',
                'actions.createOrder': '创建订单',
                'actions.resetForm': '重置表单',
                
                // 历史订单
                'history.title': '📋 历史订单管理',
                'history.export': '导出',
                'history.clear': '清空',
                'history.searchOrderId': '订单ID',
                'history.searchCustomer': '客户姓名',
                'history.searchDateFrom': '开始日期',
                'history.searchDateTo': '结束日期',
                'history.searchButton': '搜索',
                'history.resetSearch': '重置',
                'history.statTotal': '总计',
                'history.statToday': '今日',
                'history.statWeek': '本周',
                'history.statMonth': '本月',
                'history.orderList': '订单列表',
                'history.recordCount': '共 {count} 条记录',
                'history.emptyState': '暂无历史订单',
                'history.viewDetail': '查看详情',
                'history.copyOrder': '复制订单',
                'history.deleteOrder': '删除',
                
                // 提示信息
                'messages.loginSuccess': '登录成功',
                'messages.loginFailed': '登录失败',
                'messages.logoutSuccess': '已成功退出登录',
                'messages.logoutConfirm': '确定要退出登录吗？当前未保存的数据可能丢失。',
                'messages.orderCreated': '订单创建成功！',
                'messages.orderCreateFailed': '订单创建失败',
                'messages.formReset': '表单已重置',
                'messages.formResetConfirm': '确定要重置所有表单数据吗？',
                'messages.dataValidated': '数据验证通过',
                'messages.dataValidationFailed': '数据验证失败',
                'messages.emailSaved': '默认邮箱已保存',
                'messages.emailCleared': '默认邮箱已清除',
                'messages.emailInvalid': '邮箱格式不正确',
                'messages.historyExported': '历史订单已导出',
                'messages.historyCleared': '历史订单已清空',
                'messages.historyClearConfirm': '确定要清空所有历史订单吗？此操作不可恢复。',
                'messages.aiParsingSuccess': 'AI自动解析成功',
                'messages.aiParsingFailed': 'AI解析失败',
                'messages.fallbackMode': '使用基础解析模式',
                'messages.pleaseInputOrder': '请输入订单描述',
                'messages.copied': '已复制',
                'messages.copyFailed': '复制失败',

                // 图片上传
                'image.uploadTitle': '📷 图片上传分析',
                'image.uploadText': '点击或拖拽图片到此处',
                'image.uploadHint': '支持 JPG, PNG, WebP 格式，最大 5MB',
                'image.analyzing': '正在分析图片内容...',
                'image.analyzed': '图片分析完成',
                'image.noText': '未检测到文本内容',
                'image.uploadFailed': '图片上传失败',
                'image.analysisFailed': '图片分析失败',
                'image.delete': '删除图片',

                // 价格转换
                'price.originalPrice': '原价',
                'price.convertedPrice': '转换后价格',
                'price.exchangeRate': '汇率',
                'price.conversionNote': '价格已转换',
                'price.currencySettings': '汇率设置',
                'price.updateRate': '更新汇率',
                'price.resetRates': '重置汇率',

                // 多订单
                'multiOrder.title': '🔢 多订单预览与编辑',
                'multiOrder.detected': '检测到多个订单',
                'multiOrder.orderCount': '{count} 个订单',
                'multiOrder.batchCreate': '批量创建',
                'multiOrder.selectAll': '全选',
                'multiOrder.deselectAll': '取消全选',
                'multiOrder.validateAll': '验证全部',
                'multiOrder.createSelected': '创建选中订单',
                'multiOrder.selectedCount': '已选择 {count} 个订单',
                'multiOrder.orderSequence': '订单 {sequence}',
                'multiOrder.edit': '编辑',
                'multiOrder.preview': '预览',

                // 举牌服务
                'paging.detected': '检测到举牌服务',
                'paging.orderGenerated': '已自动生成举牌订单',
                'paging.service': '举牌服务',
                'paging.meetAndGreet': '迎接服务',
                'paging.pagingPoint': '举牌服务点'
            },
            
            en: {
                // Common
                'common.loading': 'Loading...',
                'common.success': 'Success',
                'common.error': 'Error',
                'common.warning': 'Warning',
                'common.info': 'Info',
                'common.confirm': 'Confirm',
                'common.cancel': 'Cancel',
                'common.close': 'Close',
                'common.save': 'Save',
                'common.delete': 'Delete',
                'common.edit': 'Edit',
                'common.search': 'Search',
                'common.reset': 'Reset',
                'common.export': 'Export',
                'common.clear': 'Clear',
                'common.copy': 'Copy',
                'common.view': 'View',
                
                // Header Navigation
                'header.title': 'OTA Order Processing System',
                'header.defaultEmail': 'Default Email:',
                'header.defaultEmailPlaceholder': 'Set default customer email',
                'header.defaultEmailTooltip': 'Set default customer email, auto-used when AI parsing cannot get email',
                'header.historyOrders': 'Order History',
                'header.logout': 'Logout',
                'header.toggleTheme': 'Toggle Theme',
                'header.language': 'Select Language',
                'header.languageZh': '中文',
                'header.languageEn': 'English',
                
                // Login Interface
                'login.title': 'User Login',
                'login.email': 'Email Address',
                'login.password': 'Password',
                'login.rememberMe': 'Remember Me',
                'login.loginButton': 'Login',
                'login.clearSaved': 'Clear Saved Account',
                'login.emailPlaceholder': 'Enter email address',
                'login.passwordPlaceholder': 'Enter password',
                
                // Smart Input Area
                'input.title': '🤖 Smart Order Parsing',
                'input.placeholder': 'Enter order description text, system will automatically parse order information...',
                'input.clearButton': 'Clear',
                'input.sampleButton': 'Sample Data',
                'input.parseButton': 'Manual Parse',
                'input.geminiStatus': 'Please enter order description',
                
                // Order Preview
                'preview.title': '📋 Order Preview & Edit',
                'preview.validate': 'Validate',
                'preview.reset': 'Reset',
                'preview.close': 'Close',
                
                // Form Fields
                'form.basicInfo': 'Basic Information',
                'form.subCategory': 'Sub Category',
                'form.subCategoryPlaceholder': 'Select sub category',
                'form.otaReference': 'OTA Reference',
                'form.otaReferencePlaceholder': 'GMH-',
                'form.otaChannel': 'OTA Channel',
                'form.otaChannelPlaceholder': 'Select OTA channel',
                'form.otaChannelCustom': 'Custom OTA',
                'form.carType': 'Car Type',
                'form.carTypePlaceholder': 'Select car type',
                'form.incharge': 'In Charge',
                'form.inchargePlaceholder': 'Select person in charge',
                
                'form.customerInfo': 'Customer Information',
                'form.customerName': 'Customer Name',
                'form.customerNamePlaceholder': 'Customer name',
                'form.customerContact': 'Contact Phone',
                'form.customerContactPlaceholder': '+60123456789',
                'form.customerEmail': 'Customer Email',
                'form.customerEmailPlaceholder': '<EMAIL>',
                'form.flightInfo': 'Flight Info',
                'form.flightInfoPlaceholder': 'MH123',
                
                'form.tripInfo': 'Trip Information',
                'form.pickup': 'Pickup Location',
                'form.pickupPlaceholder': 'Pickup location',
                'form.destination': 'Destination',
                'form.destinationPlaceholder': 'Destination',
                'form.date': 'Date',
                'form.time': 'Time',
                'form.passengerNumber': 'Passengers',
                'form.luggageNumber': 'Luggage',
                'form.specialRequests': 'Special Requests',
                'form.specialRequestsPlaceholder': 'Special requests or notes',
                
                'form.additionalInfo': 'Additional Information',
                'form.otaPrice': 'OTA Price',
                'form.otaPricePlaceholder': '0.00',
                'form.drivingRegion': 'Driving Region',
                'form.drivingRegionPlaceholder': 'Select driving region',
                'form.languages': 'Languages',
                'form.languagesPlaceholder': 'Select languages',
                
                // Action Buttons
                'actions.previewOrder': 'Preview Order',
                'actions.createOrder': 'Create Order',
                'actions.resetForm': 'Reset Form',
                
                // Order History
                'history.title': '📋 Order History Management',
                'history.export': 'Export',
                'history.clear': 'Clear',
                'history.searchOrderId': 'Order ID',
                'history.searchCustomer': 'Customer Name',
                'history.searchDateFrom': 'From Date',
                'history.searchDateTo': 'To Date',
                'history.searchButton': 'Search',
                'history.resetSearch': 'Reset',
                'history.statTotal': 'Total',
                'history.statToday': 'Today',
                'history.statWeek': 'This Week',
                'history.statMonth': 'This Month',
                'history.orderList': 'Order List',
                'history.recordCount': '{count} records total',
                'history.emptyState': 'No order history',
                'history.viewDetail': 'View Details',
                'history.copyOrder': 'Copy Order',
                'history.deleteOrder': 'Delete',
                
                // Messages
                'messages.loginSuccess': 'Login successful',
                'messages.loginFailed': 'Login failed',
                'messages.logoutSuccess': 'Successfully logged out',
                'messages.logoutConfirm': 'Are you sure you want to logout? Unsaved data may be lost.',
                'messages.orderCreated': 'Order created successfully!',
                'messages.orderCreateFailed': 'Failed to create order',
                'messages.formReset': 'Form has been reset',
                'messages.formResetConfirm': 'Are you sure you want to reset all form data?',
                'messages.dataValidated': 'Data validation passed',
                'messages.dataValidationFailed': 'Data validation failed',
                'messages.emailSaved': 'Default email saved',
                'messages.emailCleared': 'Default email cleared',
                'messages.emailInvalid': 'Invalid email format',
                'messages.historyExported': 'Order history exported',
                'messages.historyCleared': 'Order history cleared',
                'messages.historyClearConfirm': 'Are you sure you want to clear all order history? This action cannot be undone.',
                'messages.aiParsingSuccess': 'AI parsing successful',
                'messages.aiParsingFailed': 'AI parsing failed',
                'messages.fallbackMode': 'Using fallback parsing mode',
                'messages.pleaseInputOrder': 'Please enter order description',
                'messages.copied': 'Copied',
                'messages.copyFailed': 'Copy failed',

                // Image Upload
                'image.uploadTitle': '📷 Image Upload Analysis',
                'image.uploadText': 'Click or drag images here',
                'image.uploadHint': 'Supports JPG, PNG, WebP formats, max 5MB',
                'image.analyzing': 'Analyzing image content...',
                'image.analyzed': 'Image analysis completed',
                'image.noText': 'No text detected',
                'image.uploadFailed': 'Image upload failed',
                'image.analysisFailed': 'Image analysis failed',
                'image.delete': 'Delete image',

                // Price Conversion
                'price.originalPrice': 'Original Price',
                'price.convertedPrice': 'Converted Price',
                'price.exchangeRate': 'Exchange Rate',
                'price.conversionNote': 'Price converted',
                'price.currencySettings': 'Exchange Rate Settings',
                'price.updateRate': 'Update Rate',
                'price.resetRates': 'Reset Rates',

                // Multi Order
                'multiOrder.title': '🔢 Multi-Order Preview & Edit',
                'multiOrder.detected': 'Multiple orders detected',
                'multiOrder.orderCount': '{count} orders',
                'multiOrder.batchCreate': 'Batch Create',
                'multiOrder.selectAll': 'Select All',
                'multiOrder.deselectAll': 'Deselect All',
                'multiOrder.validateAll': 'Validate All',
                'multiOrder.createSelected': 'Create Selected Orders',
                'multiOrder.selectedCount': '{count} orders selected',
                'multiOrder.orderSequence': 'Order {sequence}',
                'multiOrder.edit': 'Edit',
                'multiOrder.preview': 'Preview',

                // Paging Service
                'paging.detected': 'Paging service detected',
                'paging.orderGenerated': 'Paging order auto-generated',
                'paging.service': 'Paging Service',
                'paging.meetAndGreet': 'Meet & Greet',
                'paging.pagingPoint': 'Paging Service Point'
            }
        };
    }

    /**
     * 获取翻译文本
     * @param {string} key - 翻译键
     * @param {Object} params - 参数对象
     * @returns {string} 翻译后的文本
     */
    t(key, params = {}) {
        const translation = this.translations[this.currentLanguage]?.[key] || 
                          this.translations['zh']?.[key] || 
                          key;
        
        // 替换参数
        return translation.replace(/\{(\w+)\}/g, (match, paramKey) => {
            return params[paramKey] !== undefined ? params[paramKey] : match;
        });
    }

    /**
     * 切换语言
     * @param {string} language - 语言代码
     */
    setLanguage(language) {
        if (this.translations[language]) {
            this.currentLanguage = language;
            localStorage.setItem(this.storageKey, language);
            
            // 更新界面
            this.updateUI();
            
            getLogger().log(`语言已切换为: ${language}`, 'info');
        }
    }

    /**
     * 获取当前语言
     * @returns {string} 当前语言代码
     */
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    /**
     * 更新界面文本
     */
    updateUI() {
        // 更新语言选择下拉菜单的选中状态
        const languageSelect = document.getElementById('languageSelect');
        if (languageSelect) {
            languageSelect.value = this.currentLanguage;
        }

        // 更新所有带有 data-i18n 属性的元素
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            const translation = this.t(key);

            if (element.tagName === 'INPUT' && (element.type === 'text' || element.type === 'email' || element.type === 'password')) {
                element.placeholder = translation;
            } else if (element.tagName === 'INPUT' && element.type === 'submit') {
                element.value = translation;
            } else {
                element.textContent = translation;
            }
        });
        
        // 更新所有带有 data-i18n-title 属性的元素的title
        document.querySelectorAll('[data-i18n-title]').forEach(element => {
            const key = element.getAttribute('data-i18n-title');
            element.title = this.t(key);
        });
        
        // 触发自定义事件
        document.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: this.currentLanguage }
        }));
    }

    /**
     * 获取支持的语言列表
     * @returns {Array} 语言列表
     */
    getSupportedLanguages() {
        return Object.keys(this.translations);
    }
}

// 创建全局实例
let i18nManagerInstance = null;

/**
 * 获取国际化管理器实例
 * @returns {I18nManager} 管理器实例
 */
function getI18nManager() {
    if (!i18nManagerInstance) {
        i18nManagerInstance = new I18nManager();
    }
    return i18nManagerInstance;
}

/**
 * 翻译文本的快捷函数
 * @param {string} key - 翻译键
 * @param {Object} params - 参数对象
 * @returns {string} 翻译后的文本
 */
function t(key, params = {}) {
    return getI18nManager().t(key, params);
}

// 导出到全局作用域
window.I18nManager = I18nManager;
window.getI18nManager = getI18nManager;
window.t = t;
