/**
 * 田字格网格调整器
 * 负责处理灵活网格系统的拖拽调整功能
 * <AUTHOR>
 * @version 1.0.0
 */

// 获取依赖模块（延迟获取以确保加载顺序）
function getLogger() {
    return window.OTA && window.OTA.logger || window.logger;
}

class GridResizer {
    constructor() {
        this.gridContainer = null;
        this.isResizing = false;
        this.currentHandle = null;
        this.currentPanel = null;
        this.currentColumn = null;
        this.startPosition = { x: 0, y: 0 };
        this.startGridSizes = {};
        this.minPanelHeight = 150;
        this.init();
    }

    /**
     * 初始化网格调整器
     */
    init() {
        this.gridContainer = document.querySelector('.grid-container-new');
        if (!this.gridContainer) {
            const logger = getLogger();
            if (logger) {
                logger.log('新网格容器未找到，跳过网格调整器初始化', 'warning');
            }
            return;
        }

        this.setupEventListeners();
        this.initializeGridSizes();

        const logger = getLogger();
        if (logger) {
            logger.log('新网格调整器已初始化', 'info');
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 为所有拖拽手柄添加事件监听器
        const handles = this.gridContainer.querySelectorAll('.resize-handle');
        handles.forEach(handle => {
            handle.addEventListener('mousedown', this.handleMouseDown.bind(this));
        });

        // 全局鼠标事件
        document.addEventListener('mousemove', this.handleMouseMove.bind(this));
        document.addEventListener('mouseup', this.handleMouseUp.bind(this));
        
        // 防止文本选择
        document.addEventListener('selectstart', this.preventSelection.bind(this));
    }

    /**
     * 初始化网格尺寸
     */
    initializeGridSizes() {
        // 设置默认的左列比例（3个板块）
        this.updateColumnSizes('left', [1, 1, 1]);
        // 设置默认的右列比例（4个板块）
        this.updateColumnSizes('right', [1, 1, 1, 1]);
    }

    /**
     * 处理鼠标按下事件
     */
    handleMouseDown(event) {
        event.preventDefault();

        this.isResizing = true;
        this.currentHandle = event.target;
        this.currentPanel = event.target.closest('.panel');
        this.currentColumn = event.target.closest('.column-left, .column-right');
        this.startPosition = { x: event.clientX, y: event.clientY };

        // 获取当前列的尺寸
        this.startGridSizes = this.getCurrentColumnSizes();

        // 添加调整中的样式
        this.gridContainer.classList.add('resizing');
        this.currentPanel.classList.add('resizing');

        const logger = getLogger();
        if (logger) {
            const columnType = this.currentColumn.classList.contains('column-left') ? '左列' : '右列';
            const panelType = this.currentPanel.getAttribute('data-panel');
            logger.log(`开始调整${columnType}的${panelType}板块`, 'info');
        }
    }

    /**
     * 处理鼠标移动事件
     */
    handleMouseMove(event) {
        if (!this.isResizing || !this.currentHandle) return;
        
        event.preventDefault();
        
        const deltaX = event.clientX - this.startPosition.x;
        const deltaY = event.clientY - this.startPosition.y;
        
        this.updateGridBasedOnHandle(deltaX, deltaY);
    }

    /**
     * 处理鼠标释放事件
     */
    handleMouseUp(event) {
        if (!this.isResizing) return;
        
        this.isResizing = false;
        
        // 移除调整中的样式
        this.gridContainer.classList.remove('resizing');
        const resizingItems = this.gridContainer.querySelectorAll('.grid-item.resizing');
        resizingItems.forEach(item => item.classList.remove('resizing'));
        
        this.currentHandle = null;
        
        const logger = getLogger();
        if (logger) {
            logger.log('网格调整完成', 'info');
        }
    }

    /**
     * 防止文本选择
     */
    preventSelection(event) {
        if (this.isResizing) {
            event.preventDefault();
        }
    }

    /**
     * 根据拖拽手柄更新网格 - 新的列内板块调整系统
     */
    updateGridBasedOnHandle(deltaX, deltaY) {
        if (!this.currentPanel || !this.currentColumn) return;

        const columnType = this.currentColumn.classList.contains('column-left') ? 'left' : 'right';
        const panels = this.currentColumn.querySelectorAll('.panel');
        const currentPanelIndex = Array.from(panels).indexOf(this.currentPanel);

        // 计算列容器高度
        const columnRect = this.currentColumn.getBoundingClientRect();
        const columnHeight = columnRect.height;

        // 获取当前列的尺寸比例
        const currentSizes = this.getCurrentColumnSizes()[columnType];
        const newSizes = [...currentSizes];

        // 计算新的比例
        const deltaRatio = deltaY / columnHeight;

        // 调整当前板块和下一个板块的比例
        if (currentPanelIndex < panels.length - 1) {
            const minRatio = this.minPanelHeight / columnHeight;

            // 当前板块增加，下一个板块减少
            const newCurrentRatio = Math.max(minRatio, newSizes[currentPanelIndex] + deltaRatio);
            const newNextRatio = Math.max(minRatio, newSizes[currentPanelIndex + 1] - deltaRatio);

            // 确保总比例保持不变
            const totalChange = (newCurrentRatio - newSizes[currentPanelIndex]) + (newNextRatio - newSizes[currentPanelIndex + 1]);
            if (Math.abs(totalChange) < 0.01) { // 允许小的误差
                newSizes[currentPanelIndex] = newCurrentRatio;
                newSizes[currentPanelIndex + 1] = newNextRatio;

                this.updateColumnSizes(columnType, newSizes);
            }
        }
    }

    /**
     * 更新列尺寸
     */
    updateColumnSizes(columnType, sizes) {
        const column = this.gridContainer.querySelector(`.column-${columnType}`);
        if (!column) return;

        const sizeString = sizes.map(size => `${size}fr`).join(' ');
        column.style.gridTemplateRows = sizeString;

        // 同时更新CSS变量以便其他地方使用
        sizes.forEach((size, index) => {
            this.gridContainer.style.setProperty(`--${columnType}-row-${index + 1}`, `${size}fr`);
        });
    }

    /**
     * 获取当前列尺寸
     */
    getCurrentColumnSizes() {
        const leftColumn = this.gridContainer.querySelector('.column-left');
        const rightColumn = this.gridContainer.querySelector('.column-right');

        const result = {};

        if (leftColumn) {
            const leftRows = getComputedStyle(leftColumn).gridTemplateRows.split(' ');
            result.left = leftRows.map(row => parseFloat(row) || 1);
        }

        if (rightColumn) {
            const rightRows = getComputedStyle(rightColumn).gridTemplateRows.split(' ');
            result.right = rightRows.map(row => parseFloat(row) || 1);
        }

        return result;
    }



    /**
     * 获取拖拽手柄类型
     */
    getHandleType(handle) {
        if (handle.classList.contains('resize-handle-right')) return 'column';
        if (handle.classList.contains('resize-handle-bottom')) return 'row';
        if (handle.classList.contains('resize-handle-corner')) return 'both';
        return 'unknown';
    }

    /**
     * 重置网格为默认尺寸
     */
    resetGrid() {
        this.updateColumnSizes('left', [1, 1, 1]);
        this.updateColumnSizes('right', [1, 1, 1, 1]);

        const logger = getLogger();
        if (logger) {
            logger.log('网格已重置为默认尺寸', 'info');
        }
    }

    /**
     * 销毁网格调整器
     */
    destroy() {
        if (this.gridContainer) {
            const handles = this.gridContainer.querySelectorAll('.resize-handle');
            handles.forEach(handle => {
                handle.removeEventListener('mousedown', this.handleMouseDown.bind(this));
            });
        }
        
        document.removeEventListener('mousemove', this.handleMouseMove.bind(this));
        document.removeEventListener('mouseup', this.handleMouseUp.bind(this));
        document.removeEventListener('selectstart', this.preventSelection.bind(this));
        
        const logger = getLogger();
        if (logger) {
            logger.log('网格调整器已销毁', 'info');
        }
    }
}

// 导出到全局命名空间
if (typeof window !== 'undefined') {
    if (!window.OTA) {
        window.OTA = {};
    }
    window.OTA.GridResizer = GridResizer;
    
    // 向后兼容
    window.GridResizer = GridResizer;
}
