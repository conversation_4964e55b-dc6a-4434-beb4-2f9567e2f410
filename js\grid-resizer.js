/**
 * 田字格网格调整器
 * 负责处理灵活网格系统的拖拽调整功能
 * <AUTHOR>
 * @version 1.0.0
 */

// 获取依赖模块（延迟获取以确保加载顺序）
function getLogger() {
    return window.OTA && window.OTA.logger || window.logger;
}

class GridResizer {
    constructor() {
        this.gridContainer = null;
        this.isResizing = false;
        this.currentHandle = null;
        this.startPosition = { x: 0, y: 0 };
        this.startGridSizes = { col1: 1, col2: 1, row1: 1, row2: 1 };
        this.minColWidth = 280;
        this.minRowHeight = 200;
        this.init();
    }

    /**
     * 初始化网格调整器
     */
    init() {
        this.gridContainer = document.querySelector('.grid-container');
        if (!this.gridContainer) {
            const logger = getLogger();
            if (logger) {
                logger.log('网格容器未找到，跳过网格调整器初始化', 'warning');
            }
            return;
        }

        this.setupEventListeners();
        this.initializeGridSizes();
        
        const logger = getLogger();
        if (logger) {
            logger.log('网格调整器已初始化', 'info');
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 为所有拖拽手柄添加事件监听器
        const handles = this.gridContainer.querySelectorAll('.resize-handle');
        handles.forEach(handle => {
            handle.addEventListener('mousedown', this.handleMouseDown.bind(this));
        });

        // 全局鼠标事件
        document.addEventListener('mousemove', this.handleMouseMove.bind(this));
        document.addEventListener('mouseup', this.handleMouseUp.bind(this));
        
        // 防止文本选择
        document.addEventListener('selectstart', this.preventSelection.bind(this));
    }

    /**
     * 初始化网格尺寸
     */
    initializeGridSizes() {
        // 设置默认的网格比例
        this.updateGridSizes(1, 1, 1, 1);
    }

    /**
     * 处理鼠标按下事件
     */
    handleMouseDown(event) {
        event.preventDefault();
        
        this.isResizing = true;
        this.currentHandle = event.target;
        this.startPosition = { x: event.clientX, y: event.clientY };
        
        // 获取当前网格尺寸
        this.startGridSizes = this.getCurrentGridSizes();
        
        // 添加调整中的样式
        this.gridContainer.classList.add('resizing');
        this.currentHandle.closest('.grid-item').classList.add('resizing');
        
        const logger = getLogger();
        if (logger) {
            logger.log(`开始调整网格: ${this.getHandleType(this.currentHandle)}`, 'info');
        }
    }

    /**
     * 处理鼠标移动事件
     */
    handleMouseMove(event) {
        if (!this.isResizing || !this.currentHandle) return;
        
        event.preventDefault();
        
        const deltaX = event.clientX - this.startPosition.x;
        const deltaY = event.clientY - this.startPosition.y;
        
        this.updateGridBasedOnHandle(deltaX, deltaY);
    }

    /**
     * 处理鼠标释放事件
     */
    handleMouseUp(event) {
        if (!this.isResizing) return;
        
        this.isResizing = false;
        
        // 移除调整中的样式
        this.gridContainer.classList.remove('resizing');
        const resizingItems = this.gridContainer.querySelectorAll('.grid-item.resizing');
        resizingItems.forEach(item => item.classList.remove('resizing'));
        
        this.currentHandle = null;
        
        const logger = getLogger();
        if (logger) {
            logger.log('网格调整完成', 'info');
        }
    }

    /**
     * 防止文本选择
     */
    preventSelection(event) {
        if (this.isResizing) {
            event.preventDefault();
        }
    }

    /**
     * 根据拖拽手柄更新网格
     */
    updateGridBasedOnHandle(deltaX, deltaY) {
        const handleType = this.getHandleType(this.currentHandle);
        const gridArea = this.currentHandle.closest('.grid-item').getAttribute('data-grid-area');
        
        let { col1, col2, row1, row2 } = this.startGridSizes;
        
        // 计算容器尺寸
        const containerRect = this.gridContainer.getBoundingClientRect();
        const containerWidth = containerRect.width - 32; // 减去padding
        const containerHeight = containerRect.height - 32;
        
        switch (handleType) {
            case 'column':
                if (gridArea === 'input' || gridArea === 'trip') {
                    // 左侧列调整
                    const newCol1Ratio = Math.max(
                        this.minColWidth / containerWidth,
                        (this.startGridSizes.col1 * containerWidth + deltaX) / containerWidth
                    );
                    col1 = newCol1Ratio;
                    col2 = Math.max(this.minColWidth / containerWidth, 1 - newCol1Ratio);
                }
                break;
                
            case 'row':
                if (gridArea === 'input' || gridArea === 'basic') {
                    // 上方行调整
                    const newRow1Ratio = Math.max(
                        this.minRowHeight / containerHeight,
                        (this.startGridSizes.row1 * containerHeight + deltaY) / containerHeight
                    );
                    row1 = newRow1Ratio;
                    row2 = Math.max(this.minRowHeight / containerHeight, 1 - newRow1Ratio);
                }
                break;
                
            case 'both':
                // 同时调整行列
                if (gridArea === 'input') {
                    const newCol1Ratio = Math.max(
                        this.minColWidth / containerWidth,
                        (this.startGridSizes.col1 * containerWidth + deltaX) / containerWidth
                    );
                    const newRow1Ratio = Math.max(
                        this.minRowHeight / containerHeight,
                        (this.startGridSizes.row1 * containerHeight + deltaY) / containerHeight
                    );
                    col1 = newCol1Ratio;
                    col2 = Math.max(this.minColWidth / containerWidth, 1 - newCol1Ratio);
                    row1 = newRow1Ratio;
                    row2 = Math.max(this.minRowHeight / containerHeight, 1 - newRow1Ratio);
                }
                break;
        }
        
        this.updateGridSizes(col1, col2, row1, row2);
    }

    /**
     * 更新网格尺寸
     */
    updateGridSizes(col1, col2, row1, row2) {
        this.gridContainer.style.setProperty('--grid-col-1', `${col1}fr`);
        this.gridContainer.style.setProperty('--grid-col-2', `${col2}fr`);
        this.gridContainer.style.setProperty('--grid-row-1', `${row1}fr`);
        this.gridContainer.style.setProperty('--grid-row-2', `${row2}fr`);
    }

    /**
     * 获取当前网格尺寸
     */
    getCurrentGridSizes() {
        const computedStyle = getComputedStyle(this.gridContainer);
        const cols = computedStyle.gridTemplateColumns.split(' ');
        const rows = computedStyle.gridTemplateRows.split(' ');
        
        return {
            col1: parseFloat(cols[0]) || 1,
            col2: parseFloat(cols[1]) || 1,
            row1: parseFloat(rows[0]) || 1,
            row2: parseFloat(rows[1]) || 1
        };
    }

    /**
     * 获取拖拽手柄类型
     */
    getHandleType(handle) {
        if (handle.classList.contains('resize-handle-right')) return 'column';
        if (handle.classList.contains('resize-handle-bottom')) return 'row';
        if (handle.classList.contains('resize-handle-corner')) return 'both';
        return 'unknown';
    }

    /**
     * 重置网格为默认尺寸
     */
    resetGrid() {
        this.updateGridSizes(1, 1, 1, 1);
        
        const logger = getLogger();
        if (logger) {
            logger.log('网格已重置为默认尺寸', 'info');
        }
    }

    /**
     * 销毁网格调整器
     */
    destroy() {
        if (this.gridContainer) {
            const handles = this.gridContainer.querySelectorAll('.resize-handle');
            handles.forEach(handle => {
                handle.removeEventListener('mousedown', this.handleMouseDown.bind(this));
            });
        }
        
        document.removeEventListener('mousemove', this.handleMouseMove.bind(this));
        document.removeEventListener('mouseup', this.handleMouseUp.bind(this));
        document.removeEventListener('selectstart', this.preventSelection.bind(this));
        
        const logger = getLogger();
        if (logger) {
            logger.log('网格调整器已销毁', 'info');
        }
    }
}

// 导出到全局命名空间
if (typeof window !== 'undefined') {
    if (!window.OTA) {
        window.OTA = {};
    }
    window.OTA.GridResizer = GridResizer;
    
    // 向后兼容
    window.GridResizer = GridResizer;
}
