<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI优化测试页面 - OTA订单处理系统</title>
    <link rel="stylesheet" href="style.css">
    <style>
        /* 测试页面专用样式 */
        .test-container {
            padding: var(--spacing-4);
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: var(--bg-tertiary);
            border-radius: var(--radius-lg);
            box-shadow: var(--neu-shadow-outset);
            padding: var(--spacing-4);
            margin-bottom: var(--spacing-4);
        }
        
        .test-title {
            color: var(--text-primary);
            font-size: var(--font-size-lg);
            font-weight: 600;
            margin-bottom: var(--spacing-3);
            border-bottom: 2px solid var(--color-primary);
            padding-bottom: var(--spacing-2);
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-3);
            margin-bottom: var(--spacing-3);
        }
        
        .test-item {
            background: var(--bg-primary);
            border-radius: var(--radius-md);
            padding: var(--spacing-3);
            border: 1px solid var(--border-color);
        }
        
        .test-item h4 {
            color: var(--text-accent);
            margin-bottom: var(--spacing-2);
        }
        
        .color-swatch {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-sm);
            display: inline-block;
            margin-right: var(--spacing-2);
            border: 1px solid var(--border-color);
        }
        
        .theme-demo {
            display: flex;
            gap: var(--spacing-3);
            align-items: center;
            margin-bottom: var(--spacing-2);
        }
        
        .responsive-test {
            resize: both;
            overflow: auto;
            border: 2px dashed var(--color-primary);
            min-width: 200px;
            min-height: 100px;
            padding: var(--spacing-2);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🎨 UI优化测试页面</h1>
        
        <!-- 暗色主题测试 -->
        <div class="test-section">
            <h2 class="test-title">1. 暗色主题配色测试</h2>
            <div class="test-grid">
                <div class="test-item">
                    <h4>亮色主题配色</h4>
                    <div class="theme-demo">
                        <span class="color-swatch" style="background: var(--bg-primary);"></span>
                        <span>主背景</span>
                    </div>
                    <div class="theme-demo">
                        <span class="color-swatch" style="background: var(--bg-tertiary);"></span>
                        <span>卡片背景</span>
                    </div>
                    <div class="theme-demo">
                        <span class="color-swatch" style="background: var(--color-primary);"></span>
                        <span>主色调</span>
                    </div>
                </div>
                <div class="test-item">
                    <h4>主题切换控制</h4>
                    <div class="theme-toggle">
                        <select id="languageSelect" class="language-select">
                            <option value="zh">中文</option>
                            <option value="en">English</option>
                        </select>
                        <button type="button" id="themeToggle" class="btn btn-icon" title="切换主题">🌙</button>
                    </div>
                    <p style="margin-top: var(--spacing-2); font-size: var(--font-size-sm); color: var(--text-secondary);">
                        点击月亮图标切换到暗色主题查看效果
                    </p>
                </div>
            </div>
        </div>
        
        <!-- 响应式布局测试 -->
        <div class="test-section">
            <h2 class="test-title">2. 响应式布局测试</h2>
            <div class="test-item">
                <h4>顶部控制栏响应式测试</h4>
                <div class="responsive-test">
                    <div class="header-controls">
                        <div class="persistent-email">
                            <label>默认邮箱:</label>
                            <input type="email" placeholder="设置默认客户邮箱">
                            <button class="btn btn-icon">💾</button>
                        </div>
                        <div class="user-info">
                            <span>用户信息</span>
                            <button class="btn btn-outline">历史订单</button>
                            <button class="btn btn-outline">退出登录</button>
                        </div>
                        <div class="theme-toggle">
                            <select class="language-select">
                                <option>中文</option>
                                <option>English</option>
                            </select>
                            <button class="btn btn-icon">🌙</button>
                        </div>
                    </div>
                </div>
                <p style="margin-top: var(--spacing-2); font-size: var(--font-size-sm); color: var(--text-secondary);">
                    拖拽右下角调整大小测试响应式效果
                </p>
            </div>
        </div>
        
        <!-- 灵活网格系统测试 -->
        <div class="test-section">
            <h2 class="test-title">3. 灵活网格系统测试</h2>
            <div class="test-item">
                <h4>可拖拽调整的田字格布局</h4>
                <div class="grid-container" style="height: 400px;" id="testGridContainer">
                    <div class="grid-item" data-grid-area="input">
                        <div class="resize-handle resize-handle-right"></div>
                        <div class="resize-handle resize-handle-bottom"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                        <div class="section-header">
                            <h3>📝 订单输入</h3>
                        </div>
                        <div class="input-card">
                            <div class="form-group">
                                <label>订单描述</label>
                                <div class="textarea-container">
                                    <textarea rows="3" placeholder="测试内容..."></textarea>
                                    <button type="button" class="textarea-upload-button" title="上传图片">
                                        <span class="upload-icon">📁</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="grid-item" data-grid-area="basic">
                        <div class="resize-handle resize-handle-bottom"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                        <div class="section-header">
                            <h3>📋 基本信息</h3>
                        </div>
                        <div class="form-card">
                            <div class="form-group">
                                <label>服务类型</label>
                                <select><option>请选择服务类型</option></select>
                            </div>
                            <div class="form-group">
                                <label>OTA参考号</label>
                                <input type="text" placeholder="OTA平台订单号">
                            </div>
                        </div>
                    </div>

                    <div class="grid-item" data-grid-area="trip">
                        <div class="resize-handle resize-handle-right"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                        <div class="section-header">
                            <h3>🚗 行程信息</h3>
                        </div>
                        <div class="form-card">
                            <div class="form-group">
                                <label>上车地点</label>
                                <input type="text" placeholder="上车地点">
                            </div>
                            <div class="form-group">
                                <label>目的地</label>
                                <input type="text" placeholder="目的地">
                            </div>
                        </div>
                    </div>

                    <div class="grid-item" data-grid-area="config">
                        <div class="resize-handle resize-handle-corner"></div>
                        <div class="section-header">
                            <h3>⚙️ 服务配置</h3>
                        </div>
                        <div class="form-card">
                            <div class="form-group">
                                <label>乘客人数</label>
                                <input type="number" placeholder="乘客人数">
                            </div>
                            <div class="form-group">
                                <label>行李件数</label>
                                <input type="number" placeholder="行李件数">
                            </div>

                            <!-- 整合的特殊要求区域 -->
                            <div class="section-header sub-section">
                                <h4>🔧 特殊要求</h4>
                            </div>
                            <div class="requirements-container-integrated">
                                <div class="requirements-left">
                                    <div class="form-group">
                                        <div class="checkbox-group-vertical">
                                            <label class="checkbox-label">
                                                <input type="checkbox">
                                                <span>儿童座椅</span>
                                            </label>
                                            <label class="checkbox-label">
                                                <input type="checkbox">
                                                <span>导游服务</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="requirements-right">
                                    <div class="form-group">
                                        <label>额外要求</label>
                                        <textarea rows="2" placeholder="其他特殊要求"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="margin-top: var(--spacing-3); display: flex; gap: var(--spacing-2); justify-content: center;">
                    <button type="button" id="resetGridBtn" class="btn btn-outline btn-sm">重置网格</button>
                    <button type="button" id="showGridInfoBtn" class="btn btn-outline btn-sm">显示网格信息</button>
                </div>

                <p style="margin-top: var(--spacing-2); font-size: var(--font-size-xs); color: var(--text-secondary); text-align: center;">
                    拖拽边缘和角落来调整各个区域的大小。特殊要求区域已整合到服务配置中。
                </p>
            </div>
        </div>
        
        <!-- 图片上传按钮测试 -->
        <div class="test-section">
            <h2 class="test-title">4. 图片上传按钮优化测试</h2>
            <div class="test-grid">
                <div class="test-item">
                    <h4>优化前（独立按钮）</h4>
                    <div class="image-upload-controls">
                        <button class="image-upload-button">
                            <span class="upload-icon">📁</span>
                            <span>选择图片</span>
                        </button>
                    </div>
                </div>
                <div class="test-item">
                    <h4>优化后（内嵌按钮）</h4>
                    <div class="textarea-container">
                        <textarea rows="3" placeholder="在这里输入订单描述..."></textarea>
                        <button class="textarea-upload-button" title="上传图片">
                            <span class="upload-icon">📁</span>
                        </button>
                    </div>
                    <p style="margin-top: var(--spacing-2); font-size: var(--font-size-xs); color: var(--text-secondary);">
                        图片上传按钮现在内嵌在textarea右上角
                    </p>
                </div>
            </div>
        </div>

        <!-- 特殊要求区域整合测试 -->
        <div class="test-section">
            <h2 class="test-title">5. 特殊要求区域整合测试</h2>
            <div class="test-item">
                <h4>整合到田字格中的特殊要求区域</h4>
                <div class="section-header sub-section">
                    <h4>🔧 特殊要求</h4>
                </div>
                <div class="form-card">
                    <div class="requirements-container-integrated">
                        <div class="requirements-left">
                            <div class="form-group">
                                <div class="checkbox-group-vertical">
                                    <label class="checkbox-label">
                                        <input type="checkbox">
                                        <span>儿童座椅</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox">
                                        <span>导游服务</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox">
                                        <span>迎接服务</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="requirements-right">
                            <div class="form-group">
                                <label>额外要求</label>
                                <textarea rows="3" placeholder="其他特殊要求或备注"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <p style="margin-top: var(--spacing-2); font-size: var(--font-size-xs); color: var(--text-secondary);">
                    特殊要求区域现在整合到服务配置田字格中
                </p>
            </div>
        </div>
    </div>
    
    <!-- 加载网格调整器 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/grid-resizer.js"></script>

    <script>
        // 简单的主题切换功能
        document.getElementById('themeToggle').addEventListener('click', function() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            this.textContent = newTheme === 'dark' ? '☀️' : '🌙';
        });

        // 初始化网格调整器
        let testGridResizer = null;

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化网格调整器
            if (window.OTA && window.OTA.GridResizer) {
                testGridResizer = new window.OTA.GridResizer();
                console.log('测试页面网格调整器已初始化');
            }

            // 重置网格按钮
            const resetBtn = document.getElementById('resetGridBtn');
            if (resetBtn) {
                resetBtn.addEventListener('click', function() {
                    if (testGridResizer) {
                        testGridResizer.resetGrid();
                        alert('网格已重置为默认尺寸');
                    }
                });
            }

            // 显示网格信息按钮
            const infoBtn = document.getElementById('showGridInfoBtn');
            if (infoBtn) {
                infoBtn.addEventListener('click', function() {
                    if (testGridResizer) {
                        const sizes = testGridResizer.getCurrentGridSizes();
                        const info = `当前网格尺寸：\n列1: ${sizes.col1.toFixed(2)}fr\n列2: ${sizes.col2.toFixed(2)}fr\n行1: ${sizes.row1.toFixed(2)}fr\n行2: ${sizes.row2.toFixed(2)}fr`;
                        alert(info);
                    }
                });
            }
        });
    </script>
</body>
</html>
